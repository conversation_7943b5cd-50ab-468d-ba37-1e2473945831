using Alicres.SerialPort.Exceptions;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Exceptions;

/// <summary>
/// 串口连接异常测试类
/// </summary>
public class SerialPortConnectionExceptionTests
{
    /// <summary>
    /// 测试默认构造函数
    /// </summary>
    [Fact]
    public void Constructor_Default_ShouldInitializeCorrectly()
    {
        // Act
        var exception = new SerialPortConnectionException();

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().NotBeNullOrEmpty();
        exception.PortName.Should().BeNull();
        exception.InnerException.Should().BeNull();
    }

    /// <summary>
    /// 测试带消息的构造函数
    /// </summary>
    [Fact]
    public void Constructor_WithMessage_ShouldInitializeCorrectly()
    {
        // Arrange
        const string message = "Connection failed";

        // Act
        var exception = new SerialPortConnectionException(message);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.PortName.Should().BeNull();
        exception.InnerException.Should().BeNull();
    }

    /// <summary>
    /// 测试带消息和内部异常的构造函数
    /// </summary>
    [Fact]
    public void Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly()
    {
        // Arrange
        const string message = "Connection failed";
        var innerException = new UnauthorizedAccessException("Port access denied");

        // Act
        var exception = new SerialPortConnectionException(message, innerException);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.PortName.Should().BeNull();
        exception.InnerException.Should().Be(innerException);
    }

    /// <summary>
    /// 测试带端口名称的构造函数
    /// </summary>
    [Fact]
    public void Constructor_WithPortName_ShouldInitializeCorrectly()
    {
        // Arrange
        const string message = "Connection error";
        const string portName = "COM1";

        // Act
        var exception = new SerialPortConnectionException(message, portName);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.PortName.Should().Be(portName);
        exception.InnerException.Should().BeNull();
    }

    /// <summary>
    /// 测试带端口名称和消息的构造函数
    /// </summary>
    [Fact]
    public void Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly()
    {
        // Arrange
        const string portName = "COM1";
        const string message = "Port is already in use";

        // Act
        var exception = new SerialPortConnectionException(portName, message);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.PortName.Should().Be(portName);
        exception.InnerException.Should().BeNull();
    }

    /// <summary>
    /// 测试带端口名称、消息和内部异常的构造函数
    /// </summary>
    [Fact]
    public void Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly()
    {
        // Arrange
        const string portName = "COM1";
        const string message = "Connection timeout";
        var innerException = new TimeoutException("Operation timed out");

        // Act
        var exception = new SerialPortConnectionException(portName, message, innerException);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().Be(message);
        exception.PortName.Should().Be(portName);
        exception.InnerException.Should().Be(innerException);
    }

    /// <summary>
    /// 测试异常继承关系
    /// </summary>
    [Fact]
    public void SerialPortConnectionException_ShouldInheritFromSerialPortException()
    {
        // Act
        var exception = new SerialPortConnectionException();

        // Assert
        exception.Should().BeAssignableTo<SerialPortException>();
        exception.Should().BeAssignableTo<Exception>();
    }

    /// <summary>
    /// 测试空端口名称
    /// </summary>
    [Fact]
    public void Constructor_WithNullPortName_ShouldAllowNull()
    {
        // Act
        var exception = new SerialPortConnectionException(portName: null);

        // Assert
        exception.Should().NotBeNull();
        exception.PortName.Should().BeNull();
    }

    /// <summary>
    /// 测试空消息
    /// </summary>
    [Fact]
    public void Constructor_WithNullMessage_ShouldAllowNull()
    {
        // Act
        var exception = new SerialPortConnectionException(message: null);

        // Assert
        exception.Should().NotBeNull();
        exception.Message.Should().NotBeNull(); // 默认消息
    }
}

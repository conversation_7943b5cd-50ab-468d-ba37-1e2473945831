using Alicres.SerialPort.Extensions;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;

namespace Alicres.SerialPort.Tests.Extensions;

/// <summary>
/// 服务集合扩展方法测试类
/// </summary>
public class ServiceCollectionExtensionsTests
{
    /// <summary>
    /// 测试添加串口服务
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_ShouldRegisterRequiredServices()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        services.AddAlicresSerialPort();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // 验证服务是否正确注册
        var serialPortManager = serviceProvider.GetService<ISerialPortManager>();
        serialPortManager.Should().NotBeNull();
        serialPortManager.Should().BeOfType<SerialPortManager>();
    }

    /// <summary>
    /// 测试添加串口服务时服务集合为空
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_WithNullServices_ShouldThrowException()
    {
        // Arrange
        IServiceCollection? services = null;

        // Act & Assert
        var action = () => services!.AddAlicresSerialPort();
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试添加串口服务时返回服务集合
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_ShouldReturnServiceCollection()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        var result = services.AddAlicresSerialPort();

        // Assert
        result.Should().BeSameAs(services);
    }

    /// <summary>
    /// 测试添加串口服务时注册的服务生命周期
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        services.AddAlicresSerialPort();

        // Assert
        var managerDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(ISerialPortManager));
        managerDescriptor.Should().NotBeNull();
        managerDescriptor!.Lifetime.Should().Be(ServiceLifetime.Singleton);

        var serviceDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(ISerialPortService));
        serviceDescriptor.Should().NotBeNull();
        serviceDescriptor!.Lifetime.Should().Be(ServiceLifetime.Transient);
    }

    /// <summary>
    /// 测试多次添加串口服务
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        services.AddAlicresSerialPort();
        services.AddAlicresSerialPort();

        // Assert
        var managerServices = services.Where(s => s.ServiceType == typeof(ISerialPortManager)).ToList();
        managerServices.Should().HaveCount(1); // TryAdd 防止重复注册
    }

    /// <summary>
    /// 测试服务解析
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();
        services.AddAlicresSerialPort();

        // Act
        var serviceProvider = services.BuildServiceProvider();
        var manager = serviceProvider.GetRequiredService<ISerialPortManager>();

        // Assert
        manager.Should().NotBeNull();
        manager.Should().BeOfType<SerialPortManager>();
        manager.SerialPorts.Should().NotBeNull();
        manager.SerialPorts.Should().BeEmpty();
    }

    /// <summary>
    /// 测试带配置选项的添加串口服务
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act
        services.AddAlicresSerialPort(options =>
        {
            options.EnableGlobalErrorHandling = false;
            options.EnableVerboseLogging = true;
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var manager = serviceProvider.GetService<ISerialPortManager>();
        manager.Should().NotBeNull();
    }

    /// <summary>
    /// 测试带配置选项的添加串口服务时空配置委托
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddLogging();

        // Act & Assert
        var action = () => services.AddAlicresSerialPort((Action<SerialPortServiceOptions>)null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试没有日志记录器时的行为
    /// </summary>
    [Fact]
    public void AddAlicresSerialPort_WithoutLogging_ShouldStillWork()
    {
        // Arrange
        var services = new ServiceCollection();
        // 不添加日志记录器

        // Act
        services.AddAlicresSerialPort();

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var manager = serviceProvider.GetService<ISerialPortManager>();
        manager.Should().NotBeNull();
    }

    /// <summary>
    /// 测试串口服务选项默认值
    /// </summary>
    [Fact]
    public void SerialPortServiceOptions_ShouldHaveCorrectDefaults()
    {
        // Act
        var options = new SerialPortServiceOptions();

        // Assert
        options.DefaultConfiguration.Should().BeNull();
        options.EnableGlobalErrorHandling.Should().BeTrue();
        options.EnableVerboseLogging.Should().BeFalse();
        options.GlobalReconnectOptions.Should().BeNull();
    }

    /// <summary>
    /// 测试全局重连选项默认值
    /// </summary>
    [Fact]
    public void GlobalReconnectOptions_ShouldHaveCorrectDefaults()
    {
        // Act
        var options = new GlobalReconnectOptions();

        // Assert
        options.EnableAutoReconnect.Should().BeFalse();
        options.ReconnectInterval.Should().Be(3000);
        options.MaxReconnectAttempts.Should().Be(5);
    }
}

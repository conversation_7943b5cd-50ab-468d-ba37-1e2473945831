using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// 缓冲区事件参数测试类
/// </summary>
public class BufferEventArgsTests
{
    /// <summary>
    /// 测试缓冲区警告事件参数构造函数
    /// </summary>
    [Fact]
    public void BufferWarningEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        const int usagePercentage = 75;
        const int currentLength = 1536;
        const int maxLength = 2048;

        // Act
        var eventArgs = new BufferWarningEventArgs(usagePercentage, currentLength, maxLength);

        // Assert
        eventArgs.UsagePercentage.Should().Be(usagePercentage);
        eventArgs.CurrentLength.Should().Be(currentLength);
        eventArgs.MaxLength.Should().Be(maxLength);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 测试缓冲区警告事件参数字符串表示
    /// </summary>
    [Fact]
    public void BufferWarningEventArgs_ToString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var eventArgs = new BufferWarningEventArgs(80, 1600, 2000);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("缓冲区警告");
        result.Should().Contain("80%");
        result.Should().Contain("1600/2000");
    }

    /// <summary>
    /// 测试缓冲区溢出事件参数构造函数
    /// </summary>
    [Fact]
    public void BufferOverflowEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        const int currentLength = 2048;
        const int maxLength = 2048;
        var data = new SerialPortData(new byte[] { 0x01, 0x02, 0x03 }, "test");

        // Act
        var eventArgs = new BufferOverflowEventArgs(currentLength, maxLength, data);

        // Assert
        eventArgs.CurrentLength.Should().Be(currentLength);
        eventArgs.MaxLength.Should().Be(maxLength);
        eventArgs.OverflowData.Should().Be(data);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 测试缓冲区溢出事件参数空数据抛出异常
    /// </summary>
    [Fact]
    public void BufferOverflowEventArgs_WithNullData_ShouldThrowException()
    {
        // Arrange
        const int currentLength = 2048;
        const int maxLength = 2048;

        // Act & Assert
        var action = () => new BufferOverflowEventArgs(currentLength, maxLength, null!);
        action.Should().Throw<ArgumentNullException>();
    }

    /// <summary>
    /// 测试缓冲区溢出事件参数字符串表示
    /// </summary>
    [Fact]
    public void BufferOverflowEventArgs_ToString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var data = new SerialPortData(new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05 }, "test");
        var eventArgs = new BufferOverflowEventArgs(2048, 2048, data);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("缓冲区溢出");
        result.Should().Contain("2048/2048");
        result.Should().Contain("5 字节");
    }

    /// <summary>
    /// 测试流控制状态变化事件参数构造函数
    /// </summary>
    [Fact]
    public void FlowControlStatusChangedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        const FlowControlStatus oldStatus = FlowControlStatus.Normal;
        const FlowControlStatus newStatus = FlowControlStatus.Paused;

        // Act
        var eventArgs = new FlowControlStatusChangedEventArgs(oldStatus, newStatus);

        // Assert
        eventArgs.OldStatus.Should().Be(oldStatus);
        eventArgs.NewStatus.Should().Be(newStatus);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 测试流控制状态变化事件参数字符串表示
    /// </summary>
    [Fact]
    public void FlowControlStatusChangedEventArgs_ToString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var eventArgs = new FlowControlStatusChangedEventArgs(FlowControlStatus.Normal, FlowControlStatus.Paused);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("流控制状态变化");
        result.Should().Contain("Normal -> Paused");
    }

    /// <summary>
    /// 测试拥塞检测事件参数构造函数
    /// </summary>
    [Fact]
    public void CongestionDetectedEventArgs_Constructor_ShouldInitializeCorrectly()
    {
        // Arrange
        const double currentSendRate = 1500.5;
        const int rateLimit = 2000;
        const int congestionLevel = 75;

        // Act
        var eventArgs = new CongestionDetectedEventArgs(currentSendRate, rateLimit, congestionLevel);

        // Assert
        eventArgs.CurrentSendRate.Should().Be(currentSendRate);
        eventArgs.RateLimit.Should().Be(rateLimit);
        eventArgs.CongestionLevel.Should().Be(congestionLevel);
        eventArgs.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 测试拥塞检测事件参数字符串表示
    /// </summary>
    [Fact]
    public void CongestionDetectedEventArgs_ToString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var eventArgs = new CongestionDetectedEventArgs(1500.5, 2000, 75);

        // Act
        var result = eventArgs.ToString();

        // Assert
        result.Should().Contain("拥塞检测");
        result.Should().Contain("1500.50");
        result.Should().Contain("2000");
        result.Should().Contain("75%");
    }
}

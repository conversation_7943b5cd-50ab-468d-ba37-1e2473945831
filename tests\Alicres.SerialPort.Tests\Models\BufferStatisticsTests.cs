using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// 缓冲区统计测试类
/// </summary>
public class BufferStatisticsTests
{
    /// <summary>
    /// 测试缓冲区统计构造函数
    /// </summary>
    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Act
        var statistics = new BufferStatistics();

        // Assert
        statistics.QueueLength.Should().Be(0);
        statistics.QueueUsagePercentage.Should().Be(0);
        statistics.MaxQueueLength.Should().Be(0);
        statistics.TotalBytesReceived.Should().Be(0);
        statistics.TotalBytesDropped.Should().Be(0);
        statistics.DataLossRate.Should().Be(0);
        statistics.OverflowStrategy.Should().Be(BufferOverflowStrategy.DropOldest);
        statistics.Timestamp.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// 测试数据丢失率计算
    /// </summary>
    [Fact]
    public void DataLossRate_WithData_ShouldCalculateCorrectly()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            TotalBytesReceived = 1000,
            TotalBytesDropped = 50
        };

        // Act
        var lossRate = statistics.DataLossRate;

        // Assert
        lossRate.Should().Be(5.0);
    }

    /// <summary>
    /// 测试数据丢失率计算（无接收数据）
    /// </summary>
    [Fact]
    public void DataLossRate_WithNoReceivedData_ShouldReturnZero()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            TotalBytesReceived = 0,
            TotalBytesDropped = 10
        };

        // Act
        var lossRate = statistics.DataLossRate;

        // Assert
        lossRate.Should().Be(0);
    }

    /// <summary>
    /// 测试字符串表示
    /// </summary>
    [Fact]
    public void ToString_ShouldReturnCorrectFormat()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueLength = 100,
            MaxQueueLength = 200,
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 10,
            OverflowStrategy = BufferOverflowStrategy.DropNewest
        };

        // Act
        var result = statistics.ToString();

        // Assert
        result.Should().Contain("缓冲区统计");
        result.Should().Contain("100/200");
        result.Should().Contain("50%");
        result.Should().Contain("1000 字节");
        result.Should().Contain("10 字节");
        result.Should().Contain("DropNewest");
    }

    /// <summary>
    /// 测试详细报告
    /// </summary>
    [Fact]
    public void GetDetailedReport_ShouldReturnDetailedInformation()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueLength = 150,
            MaxQueueLength = 200,
            QueueUsagePercentage = 75,
            TotalBytesReceived = 2000,
            TotalBytesDropped = 20
        };

        // Act
        var report = statistics.GetDetailedReport();

        // Assert
        report.Should().Contain("缓冲区统计报告");
        report.Should().Contain("150/200");
        report.Should().Contain("75%");
        report.Should().Contain("2,000");
        report.Should().Contain("20");
        report.Should().Contain("健康状态");
    }

    /// <summary>
    /// 测试健康状态评估 - 良好状态
    /// </summary>
    [Fact]
    public void GetHealthStatus_WithGoodCondition_ShouldReturnGood()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 0
        };

        // Act
        var status = statistics.GetHealthStatus();

        // Assert
        status.Should().Be("良好 - 运行正常");
    }

    /// <summary>
    /// 测试健康状态评估 - 危险状态
    /// </summary>
    [Fact]
    public void GetHealthStatus_WithHighUsage_ShouldReturnDangerous()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 95
        };

        // Act
        var status = statistics.GetHealthStatus();

        // Assert
        status.Should().Be("危险 - 队列使用率过高");
    }

    /// <summary>
    /// 测试健康状态评估 - 警告状态
    /// </summary>
    [Fact]
    public void GetHealthStatus_WithMediumUsage_ShouldReturnWarning()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 85
        };

        // Act
        var status = statistics.GetHealthStatus();

        // Assert
        status.Should().Be("警告 - 队列使用率较高");
    }

    /// <summary>
    /// 测试健康状态评估 - 数据丢失警告
    /// </summary>
    [Fact]
    public void GetHealthStatus_WithHighDataLoss_ShouldReturnDataLossWarning()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 60 // 6% 丢失率
        };

        // Act
        var status = statistics.GetHealthStatus();

        // Assert
        status.Should().Be("警告 - 数据丢失率较高");
    }

    /// <summary>
    /// 测试性能建议 - 良好状态
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithGoodCondition_ShouldReturnGoodMessage()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 0,
            OverflowStrategy = BufferOverflowStrategy.DropOldest
        };

        // Act
        var suggestions = statistics.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().HaveCount(1);
        suggestions[0].Should().Be("缓冲区运行状态良好，无需特别优化");
    }

    /// <summary>
    /// 测试性能建议 - 高使用率
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithHighUsage_ShouldReturnUsageSuggestion()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 85
        };

        // Act
        var suggestions = statistics.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().Contain("考虑增加队列最大长度或优化数据处理速度");
    }

    /// <summary>
    /// 测试性能建议 - 数据丢失
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithDataLoss_ShouldReturnDataLossSuggestion()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            QueueUsagePercentage = 50,
            TotalBytesReceived = 1000,
            TotalBytesDropped = 15 // 1.5% 丢失率
        };

        // Act
        var suggestions = statistics.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().Contain("数据丢失率较高，建议检查数据处理逻辑");
    }

    /// <summary>
    /// 测试性能建议 - 丢弃最新数据策略
    /// </summary>
    [Fact]
    public void GetPerformanceSuggestions_WithDropNewestStrategy_ShouldReturnStrategySuggestion()
    {
        // Arrange
        var statistics = new BufferStatistics
        {
            OverflowStrategy = BufferOverflowStrategy.DropNewest
        };

        // Act
        var suggestions = statistics.GetPerformanceSuggestions();

        // Assert
        suggestions.Should().Contain("当前使用丢弃最新数据策略，可能导致数据不连续");
    }
}

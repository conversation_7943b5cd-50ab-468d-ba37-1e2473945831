using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 流控制管理器测试类
/// </summary>
public class FlowControlManagerTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly FlowControlManager _flowControlManager;

    /// <summary>
    /// 构造函数
    /// </summary>
    public FlowControlManagerTests()
    {
        _mockLogger = new Mock<ILogger>();
        var configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };
        _flowControlManager = new FlowControlManager(configuration, _mockLogger.Object);
        _flowControlManager.FlowControlType = FlowControlType.XonXoff;
    }

    /// <summary>
    /// 测试构造函数正确初始化
    /// </summary>
    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _flowControlManager.FlowControlType.Should().Be(FlowControlType.XonXoff);
        _flowControlManager.IsEnabled.Should().BeFalse(); // 默认未启用
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试启用流控制
    /// </summary>
    [Fact]
    public void Enable_ShouldSetIsEnabledToTrue()
    {
        // Act
        _flowControlManager.IsEnabled = true;

        // Assert
        _flowControlManager.IsEnabled.Should().BeTrue();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试禁用流控制
    /// </summary>
    [Fact]
    public void Disable_ShouldSetIsEnabledToFalse()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;

        // Act
        _flowControlManager.IsEnabled = false;

        // Assert
        _flowControlManager.IsEnabled.Should().BeFalse();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试设置发送速率限制
    /// </summary>
    [Fact]
    public void SetSendRateLimit_WithValidRate_ShouldUpdateLimit()
    {
        // Act
        _flowControlManager.SendRateLimit = 1000;

        // Assert
        _flowControlManager.SendRateLimit.Should().Be(1000);
    }

    /// <summary>
    /// 测试设置负数发送速率限制
    /// </summary>
    [Fact]
    public void SetSendRateLimit_WithNegativeRate_ShouldSetToZero()
    {
        // Act
        _flowControlManager.SendRateLimit = -1;

        // Assert - 负数会被设置为0
        _flowControlManager.SendRateLimit.Should().Be(0);
    }

    /// <summary>
    /// 测试检查是否可以发送数据（未启用流控制）
    /// </summary>
    [Fact]
    public void CanSend_WhenDisabled_ShouldReturnTrue()
    {
        // Act
        var result = _flowControlManager.CanSend(100);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试检查是否可以发送数据（启用流控制）
    /// </summary>
    [Fact]
    public void CanSend_WhenEnabled_ShouldReturnTrue()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;

        // Act
        var result = _flowControlManager.CanSend(100);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试记录发送数据
    /// </summary>
    [Fact]
    public void RecordSend_WithValidData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.RecordSend(100);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试记录发送数据负数长度
    /// </summary>
    [Fact]
    public void RecordSend_WithNegativeLength_ShouldNotThrow()
    {
        // Act & Assert - 方法内部会检查IsEnabled，未启用时直接返回
        var action = () => _flowControlManager.RecordSend(-1);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理流控制数据（未启用）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WhenDisabled_ShouldNotProcess()
    {
        // Arrange
        var data = new byte[] { 0x13 }; // XOFF

        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(data);
        action.Should().NotThrow();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试处理流控制数据（启用XON/XOFF）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithXoffData_ShouldPauseFlow()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF

        // Act
        _flowControlManager.ProcessFlowControlData(xoffData);

        // Assert
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Paused);
    }

    /// <summary>
    /// 测试处理流控制数据（XON恢复）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF
        var xonData = new byte[] { 0x11 };  // XON

        // Act
        _flowControlManager.ProcessFlowControlData(xoffData);
        _flowControlManager.ProcessFlowControlData(xonData);

        // Assert
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试处理空流控制数据
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithNullData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(null!);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理空流控制数据数组
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithEmptyData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(Array.Empty<byte>());
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理非XON/XOFF流控制类型
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM2" };
        var rtsCtsManager = new FlowControlManager(configuration, _mockLogger.Object);
        rtsCtsManager.FlowControlType = FlowControlType.RtsCts;
        rtsCtsManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF

        // Act
        rtsCtsManager.ProcessFlowControlData(xoffData);

        // Assert
        rtsCtsManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
        rtsCtsManager.Dispose();
    }

    /// <summary>
    /// 测试获取统计信息
    /// </summary>
    [Fact]
    public void GetStatistics_ShouldReturnValidStatistics()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.RecordSend(100);

        // Act
        var statistics = _flowControlManager.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.FlowControlType.Should().Be(FlowControlType.XonXoff);
        statistics.IsEnabled.Should().BeTrue();
        statistics.CurrentStatus.Should().Be(FlowControlStatus.Normal);
        statistics.TotalBytesSent.Should().Be(100);
    }

    /// <summary>
    /// 测试不同流控制类型的构造
    /// </summary>
    [Theory]
    [InlineData(FlowControlType.None)]
    [InlineData(FlowControlType.XonXoff)]
    [InlineData(FlowControlType.RtsCts)]
    [InlineData(FlowControlType.Both)]
    public void Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(FlowControlType flowControlType)
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM3" };

        // Act
        using var manager = new FlowControlManager(configuration, _mockLogger.Object);
        manager.FlowControlType = flowControlType;

        // Assert
        manager.FlowControlType.Should().Be(flowControlType);
        manager.IsEnabled.Should().BeFalse();
        manager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试释放资源
    /// </summary>
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.Dispose();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试多次释放资源
    /// </summary>
    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        _flowControlManager.Dispose();
        var action = () => _flowControlManager.Dispose();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 释放测试资源
    /// </summary>
    public void Dispose()
    {
        _flowControlManager?.Dispose();
    }
}
